{"expo": {"name": "maharashtra-trek", "slug": "maharashtra-trek", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "newArchEnabled": true, "splash": {"image": "./assets/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "infoPlist": {"NSLocationWhenInUseUsageDescription": "This app uses location to show your position on the map and provide navigation to trek locations.", "NSLocationAlwaysAndWhenInUseUsageDescription": "This app uses background location to track your trek progress and provide safety features even when the app is in the background.", "NSLocationAlwaysUsageDescription": "This app uses background location to track your trek progress and provide safety features even when the app is in the background.", "NSLocationUsageDescription": "This app uses location services to track your trek progress and provide navigation assistance.", "NSMotionUsageDescription": "This app uses motion data to track your trek activities and calculate distance and elevation.", "UIBackgroundModes": ["location", "background-processing"]}, "config": {"googleMaps": {"apiKey": "AIzaSyDummy_Key_For_Development_Replace_With_Real_Key"}}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true, "permissions": ["ACCESS_FINE_LOCATION", "ACCESS_COARSE_LOCATION", "ACCESS_BACKGROUND_LOCATION"], "package": "com.ninjaaniket.maharashtratrek", "config": {"googleMaps": {"apiKey": "AIzaSyDummy_Key_For_Development_Replace_With_Real_Key"}}}, "web": {"favicon": "./assets/favicon.png"}, "plugins": [["@rnmapbox/maps", {"RNMapboxMapsImpl": "mapbox", "RNMapboxMapsDownloadToken": "**********************************************************************************************"}], ["expo-location", {"locationAlwaysAndWhenInUsePermission": "This app uses location to track your trek progress and provide navigation assistance.", "locationAlwaysPermission": "This app uses background location to track your trek progress and provide safety features even when the app is in the background.", "locationWhenInUsePermission": "This app uses location to show your position on the map and provide navigation to trek locations.", "isIosBackgroundLocationEnabled": true, "isAndroidBackgroundLocationEnabled": true}]]}}