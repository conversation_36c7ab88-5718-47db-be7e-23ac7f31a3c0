-- Data Migration for Maharashtra Trek App
INSERT INTO treks (
    id, name, category, location, difficulty, duration, elevation, description,
    starting_point_name, starting_point_latitude, starting_point_longitude,
    starting_point_facilities, starting_point_description,
    latitude, longitude, featured, rating, review_count,
    images, videos, image_key, best_time_to_visit,
    network_availability, food_and_water, accommodation, permits,
    safety_risk_level, safety_common_risks, safety_precautions,
    safety_rescue_points, safety_nearest_hospital, safety_emergency_numbers,
    weather, trek_route, how_to_reach, local_contacts
) VALUES
(33, 'Alang Fort (Alang–Madangad–Kulang / AMK)', 'fort', 'Nashik District, Maharashtra', 'Moderate', '9–12 hours (one way, Alang only); 2–3 days for full AMK circuit', '1372 meters', 'Alang Fort is part of the remote AMK trio (Alang, Madangad, Kulang), revered by seasoned trekkers for its challenging trails, iron ladders, rock climbs, thrilling exposed ledges, and large caves for camping. The summit delivers sweeping Sahyadri vistas and adventures of real wilderness trekking. Only for experienced trekkers with technical gear and strong navigation skills.', '<PERSON><PERSON><PERSON><PERSON> (primary), <PERSON><PERSON><PERSON><PERSON> (alternative)', 19.5691, 73.6411, ARRAY['Limited parking in village', 'Basic tea/snacks (seasonal)', 'No public toilets nearby'], 'Ambewadi village is the most common base for Alang, reachable via Igatpuri–Ghoti or Kasara routes. Ghatghar is used by some for alternate circuits and is slightly more developed.', 19.5691, 73.6411, true, 4.8, 59, ARRAY['https://res.cloudinary.com/dworlkdn8/image/upload/v1754044791/treks/alang/alang_1.jpg'], ARRAY[], 'alang', 'October to March (post-monsoon, water available, safe conditions)',
'{"baseVillage":{"airtel":"Fair","jio":"Fair","vodafone":"Weak","bsnl":"Fair"}}'::jsonb,
'{"atBase":{"restaurants":["Home-cooked meals"]}}'::jsonb,
'{"camping":{"allowed":true}}'::jsonb,
'{"required":false}'::jsonb,
'High',
ARRAY['Slippery, exposed ledges', 'Technical/vertical sections requiring ropes/ladder'],
ARRAY['Experienced team with technical gear', 'First aid kit'],
ARRAY['Ambewadi village', 'Ghatghar village'],
'{"name":"Ghoti Rural Hospital","distance":"40 km (approx.)","contact":"02553-221379"}'::jsonb,
'{"ambulance":"108","police":"112"}'::jsonb,
'{"monsoon":{"months":"June-September"}}'::jsonb,
'{"totalDistance":"12 km (approx. round trip to Alang only)"}'::jsonb,
'{"fromMumbai":{"byTrain":{"description":"Train to Kasara/Igatpuri"}}}'::jsonb,
'[{"name":"Lakhan Goikane","phone":"9975402070","service":"Local Guide, Food & Homestay"}]'::jsonb),

(2, 'Lohagad Fort', 'fort', 'Pune District, Maharashtra', 'Moderate', '2-3 hours (one way; base to summit)', '1,033 meters', 'Lohagad is a UNESCO World Heritage site and one of Maharashtra''s most accessible forts. Known for its massive gates, fortified ramparts, and the distinctive ''Vinchu Kata'' (scorpion''s tail) extension, Lohagad was historically an important outpost for Chhatrapati Shivaji Maharaj.', 'Lohagadwadi Village', 18.7104, 73.4763, ARRAY['Ample parking', 'Eateries', 'Basic shops', 'Local guides'], 'Lohagadwadi is the main base village, directly connected by road from Malavli/Lonavala.', 18.7104, 73.4763, true, 4.7, 321, ARRAY['https://res-console.cloudinary.com/dworlkdn8/thumbnails/v1/image/upload/v1751814903/dHJla2FwcC9mb3J0L2xvaGFnYWQvMTA3MTI5OTAtNjk4OTU0NjIwMTg4ODU1LTI2ODQ5MDc0NDU5MTkxODI3NjItbnByYWthc2gtcGl0a2FyLWZhY2Vib29rLWNyb3BwZWQtMTU2MDQxNjA2Ml9ld3EwMzI=/list_v2'], ARRAY['https://res.cloudinary.com/dworlkdn8/video/upload/v1754044854/treks/lohagad/lohagad_scenic.mp4'], 'lohagad', 'June to February (monsoon and post-monsoon/winter months)',
'{"baseVillage":{"airtel":"Good","jio":"Good","vodafone":"Good","bsnl":"Good"}}'::jsonb,
'{"atBase":{"restaurants":["Shree Ganesh Dhaba","Lohagadwadi Snacks Centre"]}}'::jsonb,
'{"camping":{"allowed":true}}'::jsonb,
'{"required":false}'::jsonb,
'Low to Moderate',
ARRAY['Slippery steps during monsoon', 'Crowds on weekends/holidays'],
ARRAY['Wear grippy shoes in wet season', 'Hydrate and pace yourself'],
ARRAY['Lohagadwadi base', 'Ganesh Darwaja (main gate)'],
'{"name":"Rural Hospital Lonavala","distance":"12 km","contact":"+91 2114-273055"}'::jsonb,
'{"ambulance":"108","police":"112"}'::jsonb,
'{"monsoon":{"months":"June-September"}}'::jsonb,
'{"totalDistance":"5 km (round trip from base to summit)"}'::jsonb,
'{"fromMumbai":{"byTrain":{"description":"Train to Lonavala or Malavli"}}}'::jsonb,
'[{"name":"Ganpat Lohagadkar","phone":"+91 9766529833","service":"Local Guide, Homestay"}]'::jsonb)

ON CONFLICT (id) DO UPDATE SET
    name = EXCLUDED.name,
    category = EXCLUDED.category,
    location = EXCLUDED.location,
    difficulty = EXCLUDED.difficulty,
    duration = EXCLUDED.duration,
    elevation = EXCLUDED.elevation,
    description = EXCLUDED.description,
    starting_point_name = EXCLUDED.starting_point_name,
    starting_point_latitude = EXCLUDED.starting_point_latitude,
    starting_point_longitude = EXCLUDED.starting_point_longitude,
    starting_point_facilities = EXCLUDED.starting_point_facilities,
    starting_point_description = EXCLUDED.starting_point_description,
    latitude = EXCLUDED.latitude,
    longitude = EXCLUDED.longitude,
    featured = EXCLUDED.featured,
    rating = EXCLUDED.rating,
    review_count = EXCLUDED.review_count,
    images = EXCLUDED.images,
    videos = EXCLUDED.videos,
    image_key = EXCLUDED.image_key,
    best_time_to_visit = EXCLUDED.best_time_to_visit,
    network_availability = EXCLUDED.network_availability,
    food_and_water = EXCLUDED.food_and_water,
    accommodation = EXCLUDED.accommodation,
    permits = EXCLUDED.permits,
    safety_risk_level = EXCLUDED.safety_risk_level,
    safety_common_risks = EXCLUDED.safety_common_risks,
    safety_precautions = EXCLUDED.safety_precautions,
    safety_rescue_points = EXCLUDED.safety_rescue_points,
    safety_nearest_hospital = EXCLUDED.safety_nearest_hospital,
    safety_emergency_numbers = EXCLUDED.safety_emergency_numbers,
    weather = EXCLUDED.weather,
    trek_route = EXCLUDED.trek_route,
    how_to_reach = EXCLUDED.how_to_reach,
    local_contacts = EXCLUDED.local_contacts,
    updated_at = NOW();