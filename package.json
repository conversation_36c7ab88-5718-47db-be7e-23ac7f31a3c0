{"name": "maharashtra-trek", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "update-data": "node scripts/updateData.js", "validate-data": "node scripts/validateData.js", "migrate-analysis": "node scripts/migrateToDataService.js", "migrate-supabase": "node scripts/migrateToSupabase.js", "validate-supabase": "node scripts/migrateToSupabase.js --validate", "test-supabase": "node scripts/testSupabaseConnection.js", "migrate-all-data": "node scripts/migrateAllData.js", "generate-sql": "node scripts/generateSQLInserts.js"}, "dependencies": {"@react-native-async-storage/async-storage": "^2.1.2", "@react-native-community/netinfo": "^11.4.1", "@react-native-community/progress-view": "^1.5.0", "@react-navigation/bottom-tabs": "^7.3.14", "@react-navigation/native": "^7.1.9", "@react-navigation/stack": "^7.3.2", "@rnmapbox/maps": "^10.1.39", "@supabase/supabase-js": "^2.53.0", "expo": "~53.0.9", "expo-av": "^15.1.6", "expo-font": "^13.3.1", "expo-linear-gradient": "~14.1.4", "expo-linking": "^7.1.5", "expo-location": "~18.1.5", "expo-sms": "^13.1.4", "expo-status-bar": "~2.2.3", "expo-task-manager": "^13.1.5", "react": "19.0.0", "react-native": "0.79.2", "react-native-gesture-handler": "~2.24.0", "react-native-maps": "1.20.1", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.10.0", "react-native-vector-icons": "^10.2.0"}, "devDependencies": {"@babel/core": "^7.20.0", "dotenv": "^17.2.1"}, "private": true}