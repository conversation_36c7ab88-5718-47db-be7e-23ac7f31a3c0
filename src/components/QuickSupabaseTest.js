/**
 * QuickSupabaseTest - Simple component to test Supabase integration
 * Add this to any screen to quickly test if Supabase is working
 */

import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Alert,
} from 'react-native';
import SupabaseService from '../services/SupabaseService';
import { COLORS } from '../constants/theme';

const QuickSupabaseTest = () => {
  const [testResults, setTestResults] = useState([]);
  const [loading, setLoading] = useState(false);

  const addResult = (test, success, message, count = null) => {
    setTestResults(prev => [...prev, {
      test,
      success,
      message,
      count,
      time: new Date().toLocaleTimeString()
    }]);
  };

  const runQuickTest = async () => {
    setLoading(true);
    setTestResults([]);

    try {
      // Test 1: Get all treks
      addResult('All Treks', false, 'Testing...', null);
      const allTreks = await SupabaseService.getAllTreks(true);
      addResult('All Treks', true, 'Success', allTreks?.length || 0);

      // Test 2: Get forts
      addResult('Forts', false, 'Testing...', null);
      const forts = await SupabaseService.getTreksByCategory('fort', true);
      addResult('Forts', true, 'Success', forts?.length || 0);

      // Test 3: Get featured
      addResult('Featured', false, 'Testing...', null);
      const featured = await SupabaseService.getFeaturedTreks(true);
      addResult('Featured', true, 'Success', featured?.length || 0);

      // Test 4: Search
      addResult('Search', false, 'Testing...', null);
      const searchResults = await SupabaseService.searchTreks('fort');
      addResult('Search', true, 'Success', searchResults?.length || 0);

    } catch (error) {
      addResult('Error', false, error.message, null);
    }

    setLoading(false);
  };

  const clearCache = async () => {
    try {
      await SupabaseService.clearCache();
      Alert.alert('Success', 'Cache cleared successfully');
    } catch (error) {
      Alert.alert('Error', error.message);
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>🧪 Supabase Quick Test</Text>
      
      <View style={styles.buttonRow}>
        <TouchableOpacity
          style={[styles.button, styles.testButton]}
          onPress={runQuickTest}
          disabled={loading}
        >
          <Text style={styles.buttonText}>
            {loading ? 'Testing...' : 'Run Test'}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, styles.clearButton]}
          onPress={clearCache}
        >
          <Text style={styles.buttonText}>Clear Cache</Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.results}>
        {testResults.map((result, index) => (
          <View
            key={index}
            style={[
              styles.resultItem,
              { backgroundColor: result.success ? '#e8f5e8' : '#ffeaea' }
            ]}
          >
            <View style={styles.resultHeader}>
              <Text style={styles.resultIcon}>
                {result.success ? '✅' : '❌'}
              </Text>
              <Text style={styles.resultTest}>{result.test}</Text>
              <Text style={styles.resultTime}>{result.time}</Text>
            </View>
            <Text style={styles.resultMessage}>
              {result.message}
              {result.count !== null && ` (${result.count} items)`}
            </Text>
          </View>
        ))}
      </ScrollView>

      <View style={styles.instructions}>
        <Text style={styles.instructionTitle}>📋 Quick Setup:</Text>
        <Text style={styles.instructionText}>
          1. Run: npm run migrate-supabase{'\n'}
          2. Check Supabase dashboard for data{'\n'}
          3. Click "Run Test" above
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#f5f5f5',
    margin: 16,
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#ddd',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 16,
    color: '#333',
  },
  buttonRow: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 16,
  },
  button: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 6,
    alignItems: 'center',
  },
  testButton: {
    backgroundColor: '#007AFF',
  },
  clearButton: {
    backgroundColor: '#FF6B6B',
  },
  buttonText: {
    color: 'white',
    fontWeight: '600',
    fontSize: 14,
  },
  results: {
    maxHeight: 200,
    marginBottom: 16,
  },
  resultItem: {
    padding: 12,
    marginBottom: 8,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#ddd',
  },
  resultHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  resultIcon: {
    fontSize: 16,
    marginRight: 8,
  },
  resultTest: {
    flex: 1,
    fontWeight: '600',
    fontSize: 14,
    color: '#333',
  },
  resultTime: {
    fontSize: 12,
    color: '#666',
  },
  resultMessage: {
    fontSize: 12,
    color: '#666',
    marginLeft: 24,
  },
  instructions: {
    backgroundColor: '#fff3cd',
    padding: 12,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#ffeaa7',
  },
  instructionTitle: {
    fontWeight: 'bold',
    marginBottom: 8,
    color: '#856404',
  },
  instructionText: {
    fontSize: 12,
    color: '#856404',
    lineHeight: 18,
  },
});

export default QuickSupabaseTest;
